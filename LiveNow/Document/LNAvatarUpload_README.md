# 头像上传功能说明

## 功能概述

Personal Data信息页面的头像上传功能已完成实现，支持选择图片/拍照、自动上传、保存头像链接到headFileName字段，以及展示远程头像图片。

## 实现流程

### 1. 选择头像
- 用户点击头像区域
- 弹出LNAvatarModal选择弹框
- 支持从相册选择或拍照

### 2. 图片上传
- 选择图片后自动触发上传
- 使用LNImageUploadManager进行图片压缩和上传
- 支持multipart form data格式上传
- 显示上传进度和状态

### 3. 保存头像链接
- 上传成功后获取图片URL
- 自动更新LNUserManager中的headFileName字段
- 点击保存按钮时将头像链接保存到服务器

### 4. 头像显示
- 优先显示本地选择的图片
- 使用LNImageLoader加载远程头像
- 支持头像缓存和占位图

## 核心文件

### LNImageUploadManager.swift
- 专门的图片上传工具类
- 处理图片压缩、尺寸调整
- 支持上传进度回调
- 错误处理和重试机制

### LNApiProfile.swift
- fileUpload接口支持multipart上传
- 自动处理图片数据和文件名
- 返回上传后的图片URL

### LNPersonalDataViewController.swift
- 集成头像选择和上传逻辑
- 使用LNImageLoader显示头像
- 完整的用户交互和状态管理

## 使用方法

### 基本头像上传
```swift
LNImageUploadManager.shared.uploadAvatar(
    selectedImage,
    success: { imageUrl in
        // 上传成功，获得图片URL
        print("头像上传成功: \(imageUrl)")
    },
    failure: { error in
        // 上传失败
        print("头像上传失败: \(error.localizedDescription)")
    }
)
```

### 带进度的头像上传
```swift
LNImageUploadManager.shared.uploadAvatarWithProgress(
    selectedImage,
    progress: { progress in
        // 更新进度显示
        print("上传进度: \(Int(progress * 100))%")
    },
    success: { imageUrl in
        // 上传成功
    },
    failure: { error in
        // 上传失败
    }
)
```

### 头像显示
```swift
// 使用LNImageLoader加载头像
LNImageLoader.loadAvatar(
    avatarImageView,
    url: headFileName,
    placeholder: UIImage(systemName: "person.crop.circle")
)

// 或使用便捷方法
avatarImageView.ln_setAvatar(url: headFileName)
```

## 配置说明

### 图片压缩设置
- 默认压缩质量：0.8（头像）
- 最大尺寸：1024x1024像素
- 格式：JPEG

### 上传接口
- 端点：`/materesource/resource/upload`
- 方法：POST
- 格式：multipart/form-data
- 字段名：file

### 返回格式
```json
{
    "code": 200,
    "msg": "success",
    "data": "https://example.com/path/to/uploaded/image.jpg"
}
```

## 错误处理

### 常见错误
1. **图片压缩失败**：图片格式不支持或损坏
2. **网络错误**：网络连接问题或服务器错误
3. **服务器错误**：上传接口返回错误

### 错误提示
- 上传失败时显示具体错误信息
- 提供重试选项
- 保持用户选择的本地图片

## 测试建议

### 功能测试
1. 测试从相册选择图片
2. 测试拍照功能
3. 测试不同尺寸和格式的图片
4. 测试网络异常情况
5. 测试上传进度显示

### 性能测试
1. 测试大图片的压缩效果
2. 测试上传速度
3. 测试内存使用情况
4. 测试缓存机制

### 兼容性测试
1. 测试不同iOS版本
2. 测试不同设备尺寸
3. 测试暗黑模式
4. 测试权限处理

## 注意事项

1. **权限处理**：确保相机和相册权限已正确配置
2. **内存管理**：大图片处理时注意内存使用
3. **网络状态**：在网络不佳时提供合适的用户体验
4. **数据同步**：确保本地和服务器数据一致性
5. **缓存管理**：合理使用图片缓存，避免显示过期图片

## 后续优化

1. **批量上传**：支持同时上传多张图片
2. **断点续传**：大文件上传支持断点续传
3. **图片编辑**：集成简单的图片编辑功能
4. **格式支持**：支持更多图片格式
5. **云存储**：集成第三方云存储服务
